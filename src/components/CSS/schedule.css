.schedule-page {
    margin-top: 120px;
}

.schedule-div {
    width: 70%;
    margin: auto;
}

.date-day {
    font-size: 1.8rem;
    font-family: "Fjalla One";
}

.event-divs {
    display: flex;
    flex-direction: column;
    font-family: 'Fjalla One';
}

.event-div {
    width: 100%;
    height: 100px;
    border: 3px solid #f2f5f7;
    margin: 10px;
    display: flex;
    align-items: center;
    transition: position 1000ms ease;
    /* Add smooth transition effect */
}

.table-visible {
    position: relative;
    top: 150px;
    /* Adjust this value as needed to move the second div down */
}

.event-timing {
    font-size: 1.4rem;
    margin-bottom: 0px;
    margin-right: 30px;
}

.vertical-line-blue {
    background-color: #0d4fad;
    width: 5px;
    height: 50%;
    margin-right: 60px;
}

.vertical-line-orange {
    background-color: #db5000;
    width: 5px;
    height: 50%;
    margin-right: 60px;
}

.vertical-line-grey {
    background-color: #ebeef0;
    width: 3px;
    height: 50%;
    margin: 0px 30px;
}

.event-icon {
    margin: 0px 30px 0px 0px;
}

.event-icon {
    width: 60px;
}

.IIT-icon {
    width: 30px;
    margin-right: 10px;
}


.buttons-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    margin-bottom: 20px;
}

.date-button {
    background-color: #fff;
    color: #41c5eb;
    padding: 15px 30px;
    border: 1px solid #41c5eb;
    border-radius: 30px;
    cursor: pointer;
    margin: 0 10px;
    font-size: 1.3rem;
}

.buttons-container-2 {
    display: flex;
    flex-direction: column;
    position: absolute;
    height: 500px;
    overflow-y: scroll;
}

.date-button-2 {
    background-color: #fff;
    color: #41c5eb;
    padding: 15px 30px;
    border: 1px solid #41c5eb;
    border-radius: 30px;
    cursor: pointer;
    margin: 0 10px;
    font-size: 1.3rem;
    width: 160px;
    margin-bottom: 20px;
}

.Dates-text {
    text-align: center;
    font-size: 1.6rem;
    font-weight: 600;
}

#style-2::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    background-color: #F5F5F5;
}

#style-2::-webkit-scrollbar {
    width: 12px;
    background-color: #F5F5F5;
}

#style-2::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
    background-color: #41c5eb;
}

@media all and (max-width:991px) {
    .schedule-div {
        width: 90%;
    }
}

@media all and (max-width:768px) {
    .schedule-div {
        width: 95%;
    }

    .event-timing {
        font-size: 1.2rem;
        margin-right: 0px;
    }

    .buttons-container {
        flex-direction: column;
        align-items: center;
    }

    .date-button {
        margin: 0 0 10px 0;
    }

    .event-icon {
        margin-right: 0px;
    }

    .buttons-container-2 {
        position: relative;
        flex-direction: row;
        height: fit-content;
        overflow-x: scroll;
        overflow-y: hidden;
    }

    .Dates-text {
        display: none;
    }
}

@media all and (max-width:685px) {
    .schedule-div {
        width: 90%;
    }

    .event-div {
        flex-direction: column;
        height: fit-content;
        padding: 10px;
        margin: 10px auto;
    }

    .event-timing {
        margin-bottom: 5px;
    }

    .league-matches {
        font-size: 1rem;
    }

    .buttons-container {
        flex-direction: row;
        overflow-x: scroll;
        margin: 10px 0px;
        justify-content: flex-start;
    }

    .date-button {
        margin: 10px 20px;
        white-space: nowrap;

    }
}

@media all and (max-width:479px) {
    .event-timing {
        margin-bottom: 1.1px;
    }

    .league-matches {
        font-size: 0.9rem;
    }
}