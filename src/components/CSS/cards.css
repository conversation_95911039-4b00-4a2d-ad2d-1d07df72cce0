.card-title::before,
.card-text::before {
    content: "";
    background-image: url('../Assests//blue.jpg');
    /* Reference the local image file */
    background-size: cover;
    background-position: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 1.6;
    z-index: -1;
}

.card-img-top {
    height: 200px;
}

.card-text-date {
    text-align: center;
    font-size: 1.2rem;
}

.card-title {
    font-weight: 500;
    font-family: 'Montserrat', sans-serif;

}

.left-aligned-header {
    font-family: "Fjalla One";
}

.cards {
    position: relative;
    top: 50px
}

.cardsX {
    font-family: "Olympic Sans", Arial, Helvetica, sans-serif;
}

.dates {
    font-size: 2rem;
    line-height: 1.8rem;
}

@media all and (max-width:991px) {
    .cards {
        position: relative;
        top: -70px !important;
    }
}