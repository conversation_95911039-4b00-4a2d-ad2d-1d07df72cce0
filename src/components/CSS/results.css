.Results-div {
  width: 70%;
  margin: 120px auto;
}

.result-divs1 {
  margin-top: 100vh;
}

.results-heading {
  font-size: 2rem;
  font-family: "Fjalla One";
}

.results-event-div {
  cursor: pointer;
  flex-direction: row !important;
}

.dropdown {
  position: relative;
  margin: 0 50px 0 auto;
}

.not-visible {
  visibility: hidden;
}

.versus-div {
  position: absolute;
  flex-direction: column;
  margin-top: 250px;
  left: 15.5% !important;
  justify-content: center;
  font-size: 1.6rem;
  height: 150px;
  width: 70% !important;
  margin: 270px auto 0px auto;
}

.scores {
  margin-bottom: 0px;
  font-size: 2.2rem;
}

.versus-div-score-text {
  display: flex;
  align-items: center;
  margin-bottom: 0px;
}

#div1,
#div2 {
  font-family: 'open sans';
  font-size: 1.3rem;
  padding: 10px 15px;
}

.toggleContainer {
  position: relative;
  top: 90px;
  left: calc((100vw - 278.5px)/2);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  width: fit-content;
  border: 3px solid #41c5eb;
  border-radius: 30px;
  background: #41c5eb;
  font-weight: bold;
  color: #41c5eb;
  cursor: pointer;
}

.toggleContainer::before {
  content: '';
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0%;
  border-radius: 30px;
  background: white;
  transition: all 0.3s;
}

.toggleCheckbox:checked+.toggleContainer::before {
  left: 50%;
}

.toggleContainer div {
  padding: 6px;
  text-align: center;
  z-index: 1;
}

.hidden {
  display: none;
}

.toggleCheckbox {
  display: none;
}

.toggleCheckbox:checked+.toggleContainer div:first-child {
  color: white;
  transition: color 0.3s;
}

.toggleCheckbox:checked+.toggleContainer div:last-child {
  color: #41c5eb;
  transition: color 0.3s;
}

.toggleCheckbox+.toggleContainer div:first-child {
  color: #41c5eb;
  transition: color 0.3s;
}

.toggleCheckbox+.toggleContainer div:last-child {
  color: white;
  transition: color 0.3s;
}

/* Adjusting colors based on checkbox state */
.toggleCheckbox:checked+.toggleContainer #div1 {
  color: white;
  transition: color 0.3s;
}

.toggleCheckbox:checked+.toggleContainer #div2 {
  color: #41c5eb;
  transition: color 0.3s;
}

@media all and (max-width:991px) {
  .results-event-div {
    flex-direction: column !important;
    height: fit-content;
  }
}

@media all and (max-width:768px) {
  .league-div {
    flex-direction: column !important;
    height: 200px;
  }

  .dropdown {
    display: none;
  }

  .league-div .vertical-line-blue {
    display: none;
  }

  .versus-div {
    margin-top: 210px;
    width: 100% !important;
    left: 0px !important;
  }
}

@media all and (max-width:479px) {
  .vertical-line-blue {
    margin-right: 0px;
  }
}