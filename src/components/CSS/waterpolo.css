.match {
    border-radius: 10px;
    background: #fff;
    box-shadow: 0 5px 10px rgb(0 57 111 / 15%);
    border-radius: 10px;
    padding: 20px;
    margin: 3% 15%;
    text-transform: uppercase;
    font-family: 'Fjalla One';
    font-size: 1.2rem;
}

.time {
    color: #18184a;
    font-style: italic;
}

.team-name-1 {
    font-size: 1.3em;
    margin-left: 2%;
    text-transform: uppercase;
    text-align: center;
}

.team-name-2 {
    text-align: center;
    font-size: 1.3em;
    margin-right: 2%;
    text-transform: uppercase;
}

.date {
    color: red;
}

.teams {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.team-logo {
    max-width: 90px;
}

.team-1,
.team-2 {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 48%;
}

.team-1 {
    justify-content: flex-start;
}

.team-2 {
    justify-content: flex-end;
}

.vs {
    color: gray;
}

.match-details {
    display: flex;
    justify-content: space-between;
}

.no-matches-block {
    height: 450px;
    text-align: center;
    font-size: 3rem;
}
.logo-img{
    width: 100%;
}

@media (max-width: 640px) {
    .team-2 {
        display: flex;
        flex-direction: column-reverse;
        margin: 0%;
    }

    .team-1 {
        display: flex;
        flex-direction: column;
        margin: 0%;
    }
    .match {
        margin: 3% 5%;    
        font-size: 1rem;
    }
    .team-name-1,.team-name-2{
        font-size: 1.2rem;
    }
    .team-logo{
        display: flex;
        justify-content: center;
    }
    .logo-img{
        width: 70%;
    }
}

.left {
    margin-left: 2vw;
}

.right {
    margin-right: 2vw;
}