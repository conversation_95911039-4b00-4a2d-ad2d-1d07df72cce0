@import url('https://fonts.googleapis.com/css2?family=Fjalla+One&family=Playfair+Display:wght@500&family=Roboto:wght@500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Raleway&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat&display=swap');

.homapage-logo {
    position: absolute;
    text-align: center;
    min-width: 20vw;
    /* height: 400px; */
    width: 400px;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    /* Center vertically */
    margin: auto;
    /* Center horizontally */
    /* left: calc(50vw - 150px); */
    left: calc(50vw - 220px);
    bottom: 25vh;
    margin-bottom: 30px;
    display: none;
}

.image-section {
    width: 100%;
    height: 70vh;
}

.image-section img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blue-section {
    /* background-image: linear-gradient(90deg,#0d4fad,#09397e); */
    display: flex;
    justify-content: center;
    align-items: center;
    height: 30vh;
}

.centered-heading {
    color: white;
    text-align: center;
    margin: 0;
    padding: 10px 0;
    margin-bottom: 80px;
    line-height: 1.5;
    font-size: 2.5rem;
    font-family: 'Montserrat', sans-serif;
    letter-spacing: 0.1rem;
    z-index: 10;
}

.home-date {
    font-family: 'open sans';
}

.name-place {
    display: flex;
    height: 50px;
}

#myVideo {
    position: absolute;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
}

@media all and (max-width:1220px) {
    .heading {
        font-size: 8rem;
    }
}

.instagramPost {
    width: 350px;
    max-width: 90%;
}

.instagram-media {
    width: 350px !important;
    min-width: 250px !important;
    height: 650px !important;
}

.Instagram-posts {
    margin-top: 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
}

.Latest-stories {
    text-align: center;
    font-size: 3rem;
    font-family: 'Fjalla One';
    margin-top: 30px;
}

.mobile-stories {
    display: none;
}
.our-partners{
    text-align: center;
    font-size: 3rem;
    font-family: 'Fjalla One';
}
.Sponsers{
    scroll-margin-top: 100px;
}
.Sponsers-div{
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    flex-wrap: wrap;
}
.sponsers-img{
    width: 300px;
    height: 300px;
    object-fit: contain;
}
.Sponser{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.Partner-type{
    font-size: 1.5rem;
    font-family: 'open sans';
    font-weight: 600;
}
.mobile-view{
    display: none;
}
@media all and (max-width:991px) {
    .heading {
        top: 37vh;
        font-size: 6rem;
        left: 20px;
    }

    .homapage-logo {
        /* height: 250px;
        width: 250px; */
        left: calc(50vw - 200px);
        bottom: 42vh;
    }

    .centered-heading {
        font-size: 2rem;
        margin-top: 60px;
    }

    .mobile-stories {
        display: block;
    }

    .laptop-stories {
        display: none;
    }

    .instagramPost {
        width: 80vw;
        margin: 0px auto;
        height: 580px;
    }

    .instagram-media {
        width: 80vw !important;
        height: 500px !important;
        margin: auto !important;
    }
    .laptop-view{
        display: none;
    }
    .mobile-view{
        display: flex;
    }
    .sponsers-img{
        width: 250px;
        height: 250px;
    }
    .Partner-type{
        font-size: 1.3rem;
    }
}

@media all and (max-width:768px) {
    .heading {
        top: 32vh;
        font-size: 6rem;
    }

    .remaining-time-div {
        font-size: 2.5rem;
        bottom: 150px;
    }

    .duration {
        font-size: 1.2rem;
    }

    .homapge-logo {
        left: calc(50vw - 150px);
    }

    .time-div p {
        margin-bottom: 10px;
    }

    .homapage-logo {
        bottom: 35vh;
    }

    .centered-heading {
        font-size: 1.7rem;
        margin-top: 60px;
    }

    .image-section {
        height: 50vh;
    }

    .blue-section {
        height: 50vh;
    }

    .IITGN-icon {
        margin-top: 5px;
        width: 30px;
        height: 30px;
    }
}

@media all and (max-width:576px) {
    .centered-heading {
        font-size: 1.5rem;
    }

    .homapage-logo {
        bottom: 30vh;
    }

    .name-place {
        flex-direction: column;
    }

    .IITGN-icon {
        position: relative;
        top: 30px;
        left: 15px;
    }

    .Latest-stories {
        font-size: 2.2rem;
    }
    .sponsers-img{
        width: 150px;
        height: 150px;
    }
    .Partner-type{
        font-size: 0.9rem;
    }
}

@media all and (max-width:479px) {
    .heading {
        top: 32vh;
        font-size: 5rem;
    }

    .centered-heading {
        font-size: 1.3rem;
        margin-top: 60px;
    }

    .homapage-logo {
        width: 300px;
        left: calc(50vw - 150px);
        bottom: calc(50vh - 190px);
    }

    .match-statement {
        width: 50%;
        text-align: center;
        margin: 10px;
    }
}

@media all and (max-width:380px) {
    .heading {
        top: 32vh;
        font-size: 4rem;
    }

    .remaining-time-div {
        font-size: 2rem;
        bottom: 150px;
    }
}