.card-image {
    width: 100%;
    /* Ensure the image covers the entire card */
    height: auto;
    /* Maintain aspect ratio */
    object-fit: cover;
    /* Scale the image while preserving aspect ratio */
    border-radius: 5px 5px 0 0;
    /* Rounded corners for the top */
    width: 150px;
    height: 150px;
    object-fit: contain;
    margin: 20px auto;
    /* Limit the height of the image */
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    text-align: center;
    margin: 10px 0;
}

.about {
    width: 100vw;
    padding: 30px 5%;
    display: flex;
    flex-direction: column;
}

.about-us {
    font-family: "Fjalla One";
    font-size: 2.5rem;
    text-align: center;
    /* Center the heading */
    margin-top: 20px;
    /* Reduce top margin */
}

.heading {
    margin-top: 50px;
    text-align: center;
    /* Center the heading */
}

.carousel-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    background-color: #fff;
    top: 50px;
}

.carousel-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    align-items: flex-start;
    /* Align cards to the top */
    width: 100%;
    max-width: 80%;
}

.carousel-card {
    display: flex;
    text-align: left;
    flex-direction: column;
    justify-content: center;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    width: calc(25% - 20px);
    /* Set width for each card */
    margin: 10px;
    height: 300px;
    /* Adjust the minimum height */
    font-family: 'open sans';
    padding-bottom: auto;
}

.card-content,
.card-info {
    font-size: 2rem;
}

.know-more {
    font-size: 1.8rem;
    /* Slightly reduce font size */
    text-align: center;
    /* Center the "Know more" link */
}

.buttons-carousel {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 70px;
}

.page-indicator {
    font-weight: bold;
    font-size: 16px;
}

.previous-carousel-button,
.next-carousel-button {
    border-radius: 50%;
    height: 40px;
    width: 40px;
    border: 0px;
    position: relative;
    bottom: 50px;
    /* Adjust button position */
}

.previous-carousel-button {
    right: 20px;
    /* Adjust button position */
}

.next-carousel-button {
    left: 20px;
    /* Adjust button position */
}

@media all and (max-width: 991px) {
    .carousel-card {
        width: 100%;
        /* Cards take full width on smaller screens */
    }

    .card-title {
        font-size: 25px;
    }

    .card-info,
    .card-content {
        font-size: 1.8rem;
    }

    .know-more {
        font-size: 1.6rem;
    }
}

@media all and (max-width: 991px) {
    .carousel-container {
        bottom: 0px;
    }
}

@media all and (max-width: 576px) {
    .carousel-card {
        width: 100%;
        /* Cards take full width on even smaller screens */
    }

    .card-title {
        font-size: 20px;
    }

    .card-info,
    .card-content {
        font-size: 1.5rem;
    }

    .know-more {
        font-size: 1.3rem;
    }
}