# Official website of INTER IIT Sports Meet 2023

##### This repository contains the source code for the interiit.in website.

## Home Page
![image](https://github.com/123nxxp/Inter-IIT-sports/assets/105475941/a4dc02f3-191e-4b61-9a9d-e1599e9cf647)


## Description
TThe project involved the development of a responsive website dedicated to the 56th Inter IIT Sports Meet, utilizing the ReactJS framework. The primary objective was to establish a centralized platform that provides extensive event information, including game schedules and results, specifically tailored for the Inter IIT event. The primary aim was to ensure convenient access to event details for participants as well as the audience.

### Events
![image](https://github.com/123nxxp/Inter-IIT-sports/assets/105475941/71de3148-c81b-451f-a39a-e7087f425632)

### Schedules
![image](https://github.com/123nxxp/Inter-IIT-sports/assets/105475941/ba498add-ae3e-42dc-a3b3-1da1a8e3ba68)
![image](https://github.com/123nxxp/Inter-IIT-sports/assets/105475941/19c3fdb7-5979-4a75-96fd-017ac33e5a28)


## Key Features

### Responsive Design : 
The website seamlessly adapts to various devices, providing an optimal user experience on desktops, tablets, and smartphones.

### Event information :
Detailed and comprehensive event information, including schedules, game details, and results

### Day Wise Results :
Present detailed results on a day-to-day basis, encompassing final outcomes, and provide an overview of the overall medal tallies earned by the IITs.

##### Overall Results
![image](https://github.com/123nxxp/Inter-IIT-sports/assets/105475941/7a713748-2a3d-4f96-8438-4b4e308d2bb5)
##### Game Wise Results:
![image](https://github.com/123nxxp/Inter-IIT-sports/assets/105475941/f578a0f6-2ad4-4dc4-a554-e5fb7960d2b9)


## Installation
To set up the project locally, follow these steps:

- Clone the repository.
- Navigate to the project directory.
- Install dependencies using [npm](https://www.npmjs.com/): `npm install`.
- Run the application: `npm start`.


## Technologies Used

- ReactJS framework
- HTML / CSS / JS


## License

Copyright © 2023, IIT Gandhinagar, All rights reserved.

## 🔗 Link
[[interiit.in]](https://interiit.in/)



## Authors

- [@Mahesh Dange](https://github.com/123nxxp/)
- [@Shriyash Mandavekar](https://github.com/Shriyash1234)
- [@Vraj Shah](https://github.com/Vraj2811)

## Feedback

If you have any feedback, please reach out to <NAME_EMAIL>



