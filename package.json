{"homepage": "https://interiit.in", "name": "interiit", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "gh-pages": "^6.0.0", "jquery": "^3.7.1", "lucide-react": "^0.277.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "react-social-media-embed": "^2.4.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "predeploy": "npm run build", "deploy": "gh-pages -d build", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}